from fastapi import FastAP<PERSON>, Request
from fastapi.responses import StreamingResponse
import httpx
import json
import subprocess
from threading import Lock
from datetime import datetime, timedelta

app = FastAPI()
OLLAMA_API_URL = "http://localhost:11434"

# --- In-memory multi-user storage ---
conversations = {}  # {client_id: {"messages": [...], "last_active": datetime}}
lock = Lock()        # for thread-safe updates

# --- GPU info endpoint ---
@app.get("/gpu")
def get_gpu_memory():
    try:
        output = subprocess.check_output(
            ["nvidia-smi", "--query-gpu=memory.free,memory.total", "--format=csv,nounits,noheader"],
            encoding="utf-8"
        )
        lines = output.strip().split("\n")
        memory_info = []
        for idx, line in enumerate(lines):
            free, total = map(int, line.split(", "))
            memory_info.append({
                "gpu": idx,
                "memory_free_mb": free,
                "memory_total_mb": total,
                "memory_used_mb": total - free
            })
        return {"gpus": memory_info}
    except FileNotFoundError:
        return {"error": "nvidia-smi not found. Is the NVIDIA driver installed?"}
    except Exception as e:
        return {"error": str(e)}

# --- Reset conversation for a client ---
@app.post("/reset")
async def reset_conversation(request: Request):
    data = await request.json()
    client_id = data.get("client_id", "default")
    with lock:
        conversations.pop(client_id, None)
    return {"message": f"Conversation for '{client_id}' has been reset."}

# --- Chat handler ---
@app.post("/chat")
async def chat(request: Request):
    data = await request.json()
    print("🚨 Incoming /chat data:", data)
    
    client_id = data.get("client_id")
    model = data.get("model", "phi4-mini-reasoning")
    stream = data.get("stream", True)
    temperature = data.get("temperature", 0.6)

    if not client_id:
        return {"error": "client_id is required"}

    new_message = data["messages"][-1] if data.get("messages") else None
    now = datetime.utcnow()

    # Initialize or update the conversation for this client
    with lock:
        if client_id not in conversations:
            system_prompt = (
                data["messages"][0]["content"]
                if data["messages"] and data["messages"][0]["role"] == "system"
                else "You are a helpful assistant."
            )
            conversations[client_id] = {
                "messages": [{"role": "system", "content": system_prompt}],
                "last_active": now,
            }

        if new_message:
            conversations[client_id]["messages"].append(new_message)
        conversations[client_id]["last_active"] = now

    payload = {
        "model": model,
        "messages": conversations[client_id]["messages"],
        "stream": stream,
        "temperature": temperature
    }

    if stream:
        async def stream_response():
            reply_text = ""
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream("POST", f"{OLLAMA_API_URL}/api/chat", json=payload) as resp:
                    async for line in resp.aiter_lines():
                        if line.strip():
                            try:
                                chunk = json.loads(line)['message']['content']
                                reply_text += chunk
                                yield chunk
                            except:
                                continue
            with lock:
                conversations[client_id]["messages"].append({"role": "assistant", "content": reply_text})
                conversations[client_id]["last_active"] = datetime.utcnow()

        return StreamingResponse(stream_response(), media_type="text/plain")

    else:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(f"{OLLAMA_API_URL}/api/chat", json=payload)
            result = response.json()
            assistant_reply = result['message']['content']
            with lock:
                conversations[client_id]["messages"].append({"role": "assistant", "content": assistant_reply})
                conversations[client_id]["last_active"] = datetime.utcnow()
            return {"response": assistant_reply}

# --- Optional cleanup routine ---
# Run this periodically in background if needed
def cleanup_idle_conversations(timeout_minutes=60):
    threshold = datetime.utcnow() - timedelta(minutes=timeout_minutes)
    with lock:
        to_delete = [cid for cid, v in conversations.items() if v["last_active"] < threshold]
        for cid in to_delete:
            print(f"Pruning idle session: {cid}")
            del conversations[cid]
