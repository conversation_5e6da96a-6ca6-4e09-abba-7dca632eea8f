  <!-- 
   sudo cp /home/<USER>/VSCodeProjects/ollama/index.html /var/www/html/ollama/index.html  
  -->

 <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>🧠 Ollama Chat</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background: #f4f7f9;
      margin: 0 auto;
      padding: 30px;
      max-width: 800px;
      color: #333;
    }

    h1 {
      text-align: center;
      color: #222;
      margin-bottom: 30px;
    }

    label {
      font-weight: 600;
      display: block;
      margin-top: 20px;
    }

    select,
    textarea,
    button {
      width: 100%;
      font-size: 1rem;
      margin-top: 8px;
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 10px;
      box-sizing: border-box;
    }

    textarea {
      height: 120px;
      resize: vertical;
    }

    button {
      background-color: #007acc;
      color: white;
      border: none;
      cursor: pointer;
      margin-top: 15px;
    }

    button:hover {
      background-color: #005fa3;
    }

    .button-row {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    #response {
      background: white;
      border: 1px solid #ccc;
      border-radius: 8px;
      padding: 15px;
      margin-top: 25px;
      min-height: 100px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h1>🧠 Ollma Chat Assistant</h1>

  <p style="background: #e9f2fa; border-left: 4px solid #007acc; padding: 12px; border-radius: 6px; font-size: 1rem;">
    <strong>Introduction:</strong> This is a test site to evaluate the performance of different AI models.
    All of these models are installed locally on our AI server and do not interact over the Internet.
    For customers who don't want their data floating around the Internet, this could be a good solution.
    <br><br>
    The server has 16 GB of GPU memory. With that amount of GPU memory, the system can host the smaller versions
    of some of the most popular large models. There are thousands (or more) models available — this is a sample
    of some models from major vendors. If we had more GPU in the server we could run the larger models.

    <br/><br/>Click the reset button before trying a different model.  
  </p>

  <p style="font-size: 0.85em; color: #888;">
    <strong>Session ID:</strong> <span id="session-id"></span>
  </p>

  <div id="gpu-info" style="margin-top: 20px; background: #fffbe6; border-left: 4px solid #f0ad4e; padding: 12px; border-radius: 6px;">
    <strong>GPU Status:</strong> <span id="gpu-status">Checking...</span>
  </div>

  <label for="model">Choose a model:</label>
  <select id="model">
    <option value="wizardlm2">WizardLM2 – Microsoft, reasoning & agent use cases</option>
    <option value="gemma3">Gemma 3 – Google, question answering, summarization, and reasoning</option>
    <option value="phi4-mini-reasoning">Phi4 – Microsoft, reasoning optimized</option>
    <option value="llama3.2">Llama 3.2 – Meta, best open-source LLM</option>
    <option value="mistral">Mistral - MistralAI, fast & efficient general LLM </option>
    <option value="dolphin3">Dolphin3 - Cog Com, general purpose, coding, math, agentic, functions </option>
    <option value="codellama">Codellama – Meta, for generating and discussing code</option>
    <option value="qwen2.5">Qwen2.5 – Alibaba, trained on data in 29 languages</option>
    <option value="deepseek-r1">Deepseek-R1 – DeepSeek, for reasoning</option>
    <option value="orca-mini">Orca Mini – Meta, compact and capable</option>
  </select>

  <label for="prompt">Your message:</label>
  <textarea id="prompt" placeholder="Ask me anything..."></textarea>

  <div class="button-row">
    <button onclick="sendMessage()">Send</button>
    <button onclick="resetConversation()" style="background-color:#777;">Reset</button>
  </div>

  <div id="response"></div>
  <script>

    
    function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      }


      let clientId = localStorage.getItem("ollama-client-id");
      if (!clientId) {
        clientId = generateUUID();  // fallback always works
        localStorage.setItem("ollama-client-id", clientId);
      }
    
    async function sendMessage() {
      const prompt = document.getElementById("prompt").value.trim();
      const model = document.getElementById("model").value;
      const responseDiv = document.getElementById("response");

      if (!prompt) {
        responseDiv.textContent = "Please enter a message.";
        return;
      }

      responseDiv.textContent = "⏳ Generating response...";

      const payload = {
        client_id: clientId,
        model: model,
        stream: true,
        temperature: 0.6,
        messages: [
          { role: "user", content: prompt }
        ]
      };

      try {
        const response = await fetch("/ollama/api/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          responseDiv.textContent = "Error: " + response.statusText;
          return;
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let output = "";

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          output += decoder.decode(value, { stream: true });
          responseDiv.textContent = output;
        }
      } catch (error) {
        responseDiv.textContent = "⚠️ Error: " + error;
      }
    }

    async function resetConversation() {
      const responseDiv = document.getElementById("response");
      responseDiv.textContent = "🔄 Resetting conversation...";
      try {
        const response = await fetch("/ollama/api/reset", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ client_id: clientId })
        });

        const result = await response.json();
        responseDiv.textContent = result.message || "✅ Conversation reset.";
      } catch (error) {
        responseDiv.textContent = "⚠️ Reset failed: " + error;
      }
    }

    async function loadGpuInfo() {
      const statusEl = document.getElementById("gpu-status");
      try {
        const response = await fetch("/ollama/api/gpu");
        const data = await response.json();

        if (data.error) {
          statusEl.textContent = "⚠️ " + data.error;
          return;
        }

        const gpuLines = data.gpus.map(gpu =>
          `GPU ${gpu.gpu}: ${gpu.memory_used_mb} MB used / ${gpu.memory_total_mb} MB total`
        );

        statusEl.textContent = gpuLines.join(" | ");
      } catch (err) {
        statusEl.textContent = "⚠️ Failed to load GPU info.";
      }
    }
    document.getElementById("session-id").textContent = clientId;

    // Call GPU info fetch when the page loads
    window.addEventListener("DOMContentLoaded", loadGpuInfo);
  </script>
</body>
</html>
